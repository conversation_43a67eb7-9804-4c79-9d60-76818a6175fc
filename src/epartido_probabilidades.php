<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidotorneo.php';
require_once __ROOT__ . '/src/classes/partidoprobabilidad.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/paisseason.php';
require_once __ROOT__ . '/src/general/preparar.php';

$id_partido               = '';
$tabselected              = 1;
$n_row_partidos_infos     = 1;
$partidos_infos           = array();
$partidos_torneos_grouped = array();
$success_display          = '';
$success_text             = '';
$error_display            = '';
$error_text               = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
			
			unset($_SESSION['id_partido']);
		} else {
			header('Location: lpartidos');
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$id_partido  = isset($_POST['id_partido']) ? limpiar_datos($_POST['id_partido']) : '';
		$id_pais     = isset($_POST['id_pais']) ? limpiar_datos($_POST['id_pais']) : '';
		$tabselected = isset($_POST['tabselected']) ? limpiar_datos($_POST["tabselected"]) : 1;

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion postsolo
#region sub_marcar_revisado_probabilidades
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_marcar_revisado_probabilidades'])) {
	try {
		Partido::modify_marcar_revisado_probabilidades($id_partido, $conexion);
		
		$next_id_partido = Partido::get_next_por_revisar_probabilidades($conexion);
		
		if (!empty($next_id_partido)) {
			$_SESSION['id_partido'] = $next_id_partido;
			
			header('Location: epartido_probabilidades');
		} else {
			header('Location: lpartidos?nnpp=1');
		}
		exit();
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_marcar_revisado_probabilidades
#region sub_upload_torneo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_upload_torneo'])) {
	try {
		$method_sub_upload_torneo = 1;

		$conexion->beginTransaction();

		$pais_upload_id = limpiar_datos($_POST['pais_upload_id']);
		$season_upload = limpiar_datos($_POST['season_upload']);
		$archivo = $_FILES['archivocsv']['tmp_name'];
		$archivocsv = file($archivo);

		$param = array();
		$param['archivocsv'] = $archivocsv;
		$param['season'] = $season_upload;
		$param['idpais'] = $pais_upload_id;
		PartidoInfo::deleteSelected($param, $conexion);
		PartidoInfo::uploadCSV($param, $conexion);

		$nom_pais_upload = Pais::get($pais_upload_id, $conexion)->nombre;

		$param = array();
		$param['nom_pais'] = $nom_pais_upload;
		$param['season'] = $season_upload;
		PartidoInfo::add_pais_season($param, $conexion);

		$conexion->commit();

		$success_display = 'show';
		$success_text = 'Los datos del torneo han sido cargados exitosamente.';

	} catch (Exception $e) {
		$conexion->rollback();

		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_upload_torneo
#region try
try {
	$update_partido_torneos = 0;
	$cur_partido            = Partido::get($id_partido, $conexion);
	$paises_torneos_info    = PartidoInfo::get_list_byhome_n_away(array('home' => $cur_partido->home, 'away' => $cur_partido->away), $conexion);
	$numero_por_revisar     = Partido::get_numero_por_revisar_probabilidades(array(), $conexion);
	
	//guardar en tabla partidos_torneos si aun no se ha agregado el torneo del partido.
	if (PartidoTorneo::existe_by_idpais_n_idpartido($cur_partido->pais_torneo->id, $id_partido, $conexion) == 0) {
		$new_partido_torneo              = new PartidoTorneo();
		$new_partido_torneo->partido->id = $id_partido;
		$new_partido_torneo->pais->id    = $cur_partido->pais_torneo->id;
		$new_partido_torneo->add($conexion);
		
		$update_partido_torneos = 1;
	}
	
	//guardar torneo seleccionado en el select de pais-torneo
	if (!empty($id_pais)) {
		$new_partido_torneo              = new PartidoTorneo();
		$new_partido_torneo->partido->id = $id_partido;
		$new_partido_torneo->pais->id    = $id_pais;
		$new_partido_torneo->add($conexion);
		
		$update_partido_torneos = 1;
	}
	
	//actualizar tabla de torneos
	$partido_torneos          = PartidoTorneo::getList($id_partido, $conexion);
	$partidos_torneos_grouped = PartidoTorneo::get_list_grouped_w_info($id_partido, $conexion);
	
	//obtener historico de partidos
	$param                       = array();
	$param['home']               = $cur_partido->home;
	$param['away']               = $cur_partido->away;
	$param['paises_adicionales'] = $partido_torneos;
	$param['orderby_fecha']      = 1;
	$partidos_infos              = PartidoInfo::getList($param, $conexion);
	
	//analizar total goles mas de 1.5
	$min_porc_viable                       = 70;
	$n_partidos_p1                         = 5;
	$n_partidos_p2                         = 10;
	$n_partidos_home_h                     = 0;
	$n_partidos_away_a                     = 0;
	$n_partidos_home                       = 0;
	$n_partidos_away                       = 0;
	$n_cumple_total_goles_masde_1_5_home_h = 0;
	$n_cumple_total_goles_masde_1_5_away_a = 0;
	$n_cumple_total_goles_masde_1_5_home   = 0;
	$n_cumple_total_goles_masde_1_5_away   = 0;
	$prob_total_goles_masde_1_5_p1_home_h  = -1;
	$prob_total_goles_masde_1_5_p2_home_h  = -1;
	$prob_total_goles_masde_1_5_p1_away_a  = -1;
	$prob_total_goles_masde_1_5_p2_away_a  = -1;
	$prob_total_goles_masde_1_5_p1_home    = -1;
	$prob_total_goles_masde_1_5_p2_home    = -1;
	$prob_total_goles_masde_1_5_p1_away    = -1;
	$prob_total_goles_masde_1_5_p2_away    = -1;
	$n_cumple_home_marca_home_h            = 0;
	$n_cumple_home_marca_home              = 0;
	$prob_home_marca_p1_home_h             = -1;
	$prob_home_marca_p2_home_h             = -1;
	$prob_home_marca_p1_home               = -1;
	$prob_home_marca_p2_home               = -1;
	$n_cumple_home_superior_1_5_home_h     = 0;
	$n_cumple_home_superior_1_5_home       = 0;
	$prob_home_superior_1_5_p1_home_h      = -1;
	$prob_home_superior_1_5_p2_home_h      = -1;
	$prob_home_superior_1_5_p1_home        = -1;
	$prob_home_superior_1_5_p2_home        = -1;
	$n_cumple_away_marca_away_a            = 0;
	$n_cumple_away_marca_away              = 0;
	$prob_away_marca_p1_away_a             = -1;
	$prob_away_marca_p2_away_a             = -1;
	$prob_away_marca_p1_away               = -1;
	$prob_away_marca_p2_away               = -1;
	$n_cumple_away_superior_1_5_away_a     = 0;
	$n_cumple_away_superior_1_5_away       = 0;
	$prob_away_superior_1_5_p1_away_a      = -1;
	$prob_away_superior_1_5_p2_away_a      = -1;
	$prob_away_superior_1_5_p1_away        = -1;
	$prob_away_superior_1_5_p2_away        = -1;
	$n_cumple_ambos_marcan_home_h          = 0;
	$n_cumple_ambos_marcan_away_a          = 0;
	$n_cumple_ambos_marcan_home            = 0;
	$n_cumple_ambos_marcan_away            = 0;
	$prob_ambos_marcan_p1_home_h           = -1;
	$prob_ambos_marcan_p2_home_h           = -1;
	$prob_ambos_marcan_p1_away_a           = -1;
	$prob_ambos_marcan_p2_away_a           = -1;
	$prob_ambos_marcan_p1_home             = -1;
	$prob_ambos_marcan_p2_home             = -1;
	$prob_ambos_marcan_p1_away             = -1;
	$prob_ambos_marcan_p2_away             = -1;
	$total_goals_home                      = 0;
	$total_goals_away                      = 0;
	$avg_goals_p1_home                     = -1;
	$avg_goals_p2_home                     = -1;
	$avg_goals_p1_away                     = -1;
	$avg_goals_p2_away                     = -1;
	$total_conceded_home                   = 0;
	$total_conceded_away                   = 0;
	$avg_conceded_p1_home                  = -1;
	$avg_conceded_p2_home                  = -1;
	$avg_conceded_p1_away                  = -1;
	$avg_conceded_p2_away                  = -1;
	
	/** @var PartidoInfo[] $partidos_infos */
	foreach ($partidos_infos as $partido_info) {
		$total_goles = $partido_info->homegoals + $partido_info->awaygoals;
		
		//sum goals to each team so that we get the avg per number of matches
		//sum conceded goals to each team so that we get the avg per number of matches
		if ($cur_partido->home == $partido_info->home) {
			$total_goals_home    += $partido_info->homegoals;
			$total_conceded_home += $partido_info->awaygoals;
		}
		if ($cur_partido->home == $partido_info->away) {
			$total_goals_home    += $partido_info->awaygoals;
			$total_conceded_home += $partido_info->homegoals;
		}
		if ($cur_partido->away == $partido_info->home) {
			$total_goals_away    += $partido_info->homegoals;
			$total_conceded_away += $partido_info->awaygoals;
		}
		if ($cur_partido->away == $partido_info->away) {
			$total_goals_away    += $partido_info->awaygoals;
			$total_conceded_away += $partido_info->homegoals;
		}
		
		//partidos home @home
		if ($cur_partido->home == $partido_info->home) {
			$n_partidos_home_h++;
			$n_cumple_home_marca_home_h            += ($partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_home_superior_1_5_home_h     += ($partido_info->homegoals > 1.5) ? 1 : 0;
			$n_cumple_ambos_marcan_home_h          += ($partido_info->homegoals > 0.5 && $partido_info->awaygoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_home_h += ($total_goles > 1.5) ? 1 : 0;
		}
		
		if ($n_partidos_home_h == $n_partidos_p1) {
			$prob_home_marca_p1_home_h            = ceil(($n_cumple_home_marca_home_h * 100) / $n_partidos_p1);
			$prob_home_superior_1_5_p1_home_h     = ceil(($n_cumple_home_superior_1_5_home_h * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_home_h          = ceil(($n_cumple_ambos_marcan_home_h * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_home_h = ceil(($n_cumple_total_goles_masde_1_5_home_h * 100) / $n_partidos_p1);
		}
		if ($n_partidos_home_h == $n_partidos_p2) {
			$prob_home_marca_p2_home_h            = ceil(($n_cumple_home_marca_home_h * 100) / $n_partidos_p2);
			$prob_home_superior_1_5_p2_home_h     = ceil(($n_cumple_home_superior_1_5_home_h * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_home_h          = ceil(($n_cumple_ambos_marcan_home_h * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_home_h = ceil(($n_cumple_total_goles_masde_1_5_home_h * 100) / $n_partidos_p2);
		}
		
		//partidos away @away
		if ($cur_partido->away == $partido_info->away) {
			$n_partidos_away_a++;
			$n_cumple_away_marca_away_a            += ($partido_info->awaygoals > 0.5) ? 1 : 0;
			$n_cumple_away_superior_1_5_away_a     += ($partido_info->awaygoals > 1.5) ? 1 : 0;
			$n_cumple_ambos_marcan_away_a          += ($partido_info->awaygoals > 0.5 && $partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_away_a += ($total_goles > 1.5) ? 1 : 0;
		}
		
		if ($n_partidos_away_a == $n_partidos_p1) {
			$prob_away_marca_p1_away_a            = ceil(($n_cumple_away_marca_away_a * 100) / $n_partidos_p1);
			$prob_away_superior_1_5_p1_away_a     = ceil(($n_cumple_away_superior_1_5_away_a * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_away_a          = ceil(($n_cumple_ambos_marcan_away_a * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_away_a = ceil(($n_cumple_total_goles_masde_1_5_away_a * 100) / $n_partidos_p1);
		}
		if ($n_partidos_away_a == $n_partidos_p2) {
			$prob_away_marca_p2_away_a            = ceil(($n_cumple_away_marca_away_a * 100) / $n_partidos_p2);
			$prob_away_superior_1_5_p2_away_a     = ceil(($n_cumple_away_superior_1_5_away_a * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_away_a          = ceil(($n_cumple_ambos_marcan_away_a * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_away_a = ceil(($n_cumple_total_goles_masde_1_5_away_a * 100) / $n_partidos_p2);
		}
		
		//partidos home
		if ($cur_partido->home == $partido_info->home || $cur_partido->home == $partido_info->away) {
			$n_partidos_home++;
			
			if ($cur_partido->home == $partido_info->home) {
				$n_cumple_home_marca_home        += ($partido_info->homegoals > 0.5) ? 1 : 0;
				$n_cumple_home_superior_1_5_home += ($partido_info->homegoals > 1.5) ? 1 : 0;
			}
			if ($cur_partido->home == $partido_info->away) {
				$n_cumple_home_marca_home        += ($partido_info->awaygoals > 0.5) ? 1 : 0;
				$n_cumple_home_superior_1_5_home += ($partido_info->awaygoals > 1.5) ? 1 : 0;
			}
			
			$n_cumple_ambos_marcan_home          += ($partido_info->awaygoals > 0.5 && $partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_home += ($total_goles > 1.5) ? 1 : 0;
		}
		
		if ($n_partidos_home == $n_partidos_p1) {
			$prob_home_marca_p1_home            = ceil(($n_cumple_home_marca_home * 100) / $n_partidos_p1);
			$prob_home_superior_1_5_p1_home     = ceil(($n_cumple_home_superior_1_5_home * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_home          = ceil(($n_cumple_ambos_marcan_home * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_home = ceil(($n_cumple_total_goles_masde_1_5_home * 100) / $n_partidos_p1);
			$avg_goals_p1_home                  = round($total_goals_home / $n_partidos_p1, 2);
			$avg_conceded_p1_home               = round($total_conceded_home / $n_partidos_p1, 2);
		}
		if ($n_partidos_home == $n_partidos_p2) {
			$prob_home_marca_p2_home            = ceil(($n_cumple_home_marca_home * 100) / $n_partidos_p2);
			$prob_home_superior_1_5_p2_home     = ceil(($n_cumple_home_superior_1_5_home * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_home          = ceil(($n_cumple_ambos_marcan_home * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_home = ceil(($n_cumple_total_goles_masde_1_5_home * 100) / $n_partidos_p2);
			$avg_goals_p2_home                  = round($total_goals_home / $n_partidos_p2, 2);
			$avg_conceded_p2_home               = round($total_conceded_home / $n_partidos_p2, 2);
		}
		
		//partidos away
		if ($cur_partido->away == $partido_info->home || $cur_partido->away == $partido_info->away) {
			$n_partidos_away++;
			
			if ($cur_partido->away == $partido_info->home) {
				$n_cumple_away_marca_away        += ($partido_info->homegoals > 0.5) ? 1 : 0;
				$n_cumple_away_superior_1_5_away += ($partido_info->homegoals > 1.5) ? 1 : 0;
			}
			if ($cur_partido->away == $partido_info->away) {
				$n_cumple_away_marca_away        += ($partido_info->awaygoals > 0.5) ? 1 : 0;
				$n_cumple_away_superior_1_5_away += ($partido_info->awaygoals > 1.5) ? 1 : 0;
			}
			
			$n_cumple_ambos_marcan_away          += ($partido_info->awaygoals > 0.5 && $partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_away += ($total_goles > 1.5) ? 1 : 0;
		}
		
		if ($n_partidos_away == $n_partidos_p1) {
			$prob_away_marca_p1_away            = ceil(($n_cumple_away_marca_away * 100) / $n_partidos_p1);
			$prob_away_superior_1_5_p1_away     = ceil(($n_cumple_away_superior_1_5_away * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_away          = ceil(($n_cumple_ambos_marcan_away * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_away = ceil(($n_cumple_total_goles_masde_1_5_away * 100) / $n_partidos_p1);
			$avg_goals_p1_away                  = round($total_goals_away / $n_partidos_p1, 2);
			$avg_conceded_p1_away               = round($total_conceded_away / $n_partidos_p1, 2);
		}
		if ($n_partidos_away == $n_partidos_p2) {
			$prob_away_marca_p2_away            = ceil(($n_cumple_away_marca_away * 100) / $n_partidos_p2);
			$prob_away_superior_1_5_p2_away     = ceil(($n_cumple_away_superior_1_5_away * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_away          = ceil(($n_cumple_ambos_marcan_away * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_away = ceil(($n_cumple_total_goles_masde_1_5_away * 100) / $n_partidos_p2);
			$avg_goals_p2_away                  = round($total_goals_away / $n_partidos_p2, 2);
			$avg_conceded_p2_away               = round($total_conceded_away / $n_partidos_p2, 2);
		}
	}
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try
#region sub_agregar_prob_total_goles_masde_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_total_goles_masde_1_5'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::TOTAL_GOLES_MASDE_1_5;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_total_goles_masde_1_5_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_total_goles_masde_1_5_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_total_goles_masde_1_5_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_total_goles_masde_1_5_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_total_goles_masde_1_5_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_total_goles_masde_1_5_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_total_goles_masde_1_5_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_total_goles_masde_1_5_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_1_5']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de total goles mas de 1.5 han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_total_goles_masde_1_5
#region sub_agregar_prob_home_marca
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_home_marca'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::HOME_MARCA;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_home_marca_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_home_marca_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = -1;
		$new_partido_probabilidad->p1_away_a            = -1;
		$new_partido_probabilidad->p1_away              = -1;
		$new_partido_probabilidad->p1_avg_away          = -1;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_home_marca_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_home_marca_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = -1;
		$new_partido_probabilidad->p2_away_a            = -1;
		$new_partido_probabilidad->p2_away              = -1;
		$new_partido_probabilidad->p2_avg_away          = -1;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_home_marca']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de home marca han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_home_marca
#region sub_agregar_prob_home_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_home_superior_1_5'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::HOME_SUPERIOR_1_5;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_home_superior_1_5_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_home_superior_1_5_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = -1;
		$new_partido_probabilidad->p1_away_a            = -1;
		$new_partido_probabilidad->p1_away              = -1;
		$new_partido_probabilidad->p1_avg_away          = -1;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_home_superior_1_5_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_home_superior_1_5_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = -1;
		$new_partido_probabilidad->p2_away_a            = -1;
		$new_partido_probabilidad->p2_away              = -1;
		$new_partido_probabilidad->p2_avg_away          = -1;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_home_superior_1_5']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de home SUPERIOR 1.5 han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_home_superior_1_5
#region sub_agregar_prob_away_marca
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_away_marca'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::AWAY_MARCA;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = -1;
		$new_partido_probabilidad->p1_home              = -1;
		$new_partido_probabilidad->p1_avg_home          = -1;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_away_marca_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_away_marca_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = -1;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = -1;
		$new_partido_probabilidad->p2_home              = -1;
		$new_partido_probabilidad->p2_avg_home          = -1;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_away_marca_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_away_marca_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = -1;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_away_marca']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de away marca han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_away_marca
#region sub_agregar_prob_away_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_away_superior_1_5'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::AWAY_SUPERIOR_1_5;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = -1;
		$new_partido_probabilidad->p1_home              = -1;
		$new_partido_probabilidad->p1_avg_home          = -1;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_away_superior_1_5_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_away_superior_1_5_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = -1;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = -1;
		$new_partido_probabilidad->p2_home              = -1;
		$new_partido_probabilidad->p2_avg_home          = -1;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_away_superior_1_5_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_away_superior_1_5_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = -1;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_away_superior_1_5']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de away SUPERIOR 1.5 han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_away_superior_1_5
#region sub_agregar_prob_ambos_marcan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_ambos_marcan'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::AMBOS_MARCAN;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_ambos_marcan_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_ambos_marcan_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_ambos_marcan_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_ambos_marcan_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_ambos_marcan_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_ambos_marcan_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_ambos_marcan_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_ambos_marcan_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_ambos_marcan']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de ambos marcan han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_ambos_marcan
#region sub_agregar_custom
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_custom'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = limpiar_datos($_POST['tipo_apuesta_custom']);
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = -1;
		$new_partido_probabilidad->p1_home              = -1;
		$new_partido_probabilidad->p1_avg_home          = -1;
		$new_partido_probabilidad->p1_avg_conceded_home = -1;
		$new_partido_probabilidad->p1_away_a            = -1;
		$new_partido_probabilidad->p1_away              = -1;
		$new_partido_probabilidad->p1_avg_away          = -1;
		$new_partido_probabilidad->p1_avg_conceded_away = -1;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = -1;
		$new_partido_probabilidad->p2_home              = -1;
		$new_partido_probabilidad->p2_avg_home          = -1;
		$new_partido_probabilidad->p2_avg_conceded_home = -1;
		$new_partido_probabilidad->p2_away_a            = -1;
		$new_partido_probabilidad->p2_away              = -1;
		$new_partido_probabilidad->p2_avg_away          = -1;
		$new_partido_probabilidad->p2_avg_conceded_away = -1;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_custom']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de apuesta custom han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_custom

require_once __ROOT__ . '/views/epartido_probabilidades.view.php';

?>


